"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON>, X, <PERSON>L<PERSON><PERSON>, ArrowRight } from "lucide-react";
import Flashcard, { exampleFlashcards } from "@/components/ui/learning/Flashcard";

interface FlashcardPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  conceptTitle: string;
}

export default function FlashcardPreviewModal({
  isOpen,
  onClose,
  conceptTitle,
}: FlashcardPreviewModalProps) {
  const [currentCardIndex, setCurrentCardIndex] = useState(0);

  const handleNextCard = () => {
    setCurrentCardIndex((prev) => (prev + 1) % exampleFlashcards.length);
  };

  const handlePrevCard = () => {
    setCurrentCardIndex((prev) => (prev - 1 + exampleFlashcards.length) % exampleFlashcards.length);
  };

  const handleClose = () => {
    setCurrentCardIndex(0);
    onClose();
  };

  const currentCard = exampleFlashcards[currentCardIndex];

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
          style={{ backgroundColor: "rgba(0, 0, 0, 0.5)" }}
        >
          <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.95, opacity: 0 }}
            className="relative w-full max-w-2xl max-h-[90vh] overflow-hidden rounded-2xl"
            style={{ backgroundColor: "var(--white)" }}
          >
            {/* Header */}
            <div
              className="flex items-center justify-between p-6 border-b"
              style={{ borderColor: "var(--color-border)" }}
            >
              <div className="flex items-center gap-3">
                <div
                  className="p-2 rounded-lg"
                  style={{ backgroundColor: "var(--emerald)", color: "var(--white)" }}
                >
                  <Eye className="w-5 h-5" />
                </div>
                <div>
                  <h2
                    className="text-lg font-semibold"
                    style={{ color: "var(--charcoal)" }}
                  >
                    Flashcard Preview
                  </h2>
                  <p
                    className="text-sm"
                    style={{ color: "var(--grey)" }}
                  >
                    {conceptTitle} • Card {currentCardIndex + 1} of {exampleFlashcards.length}
                  </p>
                </div>
              </div>
              <button
                onClick={handleClose}
                className="p-2 rounded-lg transition-colors duration-200"
                style={{ 
                  backgroundColor: "var(--color-muted)",
                  color: "var(--grey)"
                }}
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Content */}
            <div
              className="flex flex-col items-center justify-center p-8"
              style={{
                backgroundColor: "var(--color-muted)",
                minHeight: "400px"
              }}
            >
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentCardIndex}
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -50 }}
                  transition={{ duration: 0.3 }}
                >
                  <Flashcard
                    data={currentCard}
                    onFlip={(isFlipped) => {
                      console.log(`Flashcard ${currentCardIndex + 1} flipped:`, isFlipped);
                    }}
                  />
                </motion.div>
              </AnimatePresence>

              {/* Navigation */}
              <div className="flex items-center gap-4 mt-8">
                <button
                  onClick={handlePrevCard}
                  className="p-3 rounded-xl transition-all duration-200 hover:scale-105"
                  style={{
                    backgroundColor: "var(--white)",
                    color: "var(--charcoal)",
                    border: "1px solid var(--color-border)"
                  }}
                  disabled={exampleFlashcards.length <= 1}
                >
                  <ArrowLeft className="w-5 h-5" />
                </button>

                <div
                  className="px-4 py-2 rounded-lg text-sm font-medium"
                  style={{
                    backgroundColor: "var(--white)",
                    color: "var(--charcoal)",
                    border: "1px solid var(--color-border)"
                  }}
                >
                  {currentCard.category}
                </div>

                <button
                  onClick={handleNextCard}
                  className="p-3 rounded-xl transition-all duration-200 hover:scale-105"
                  style={{
                    backgroundColor: "var(--white)",
                    color: "var(--charcoal)",
                    border: "1px solid var(--color-border)"
                  }}
                  disabled={exampleFlashcards.length <= 1}
                >
                  <ArrowRight className="w-5 h-5" />
                </button>
              </div>

              {/* Instructions */}
              <p
                className="text-sm text-center mt-6 max-w-md"
                style={{ color: "var(--grey)" }}
              >
                Click the flashcard to flip it and reveal the answer. Use the navigation buttons to browse through different examples.
              </p>
            </div>

            {/* Footer */}
            <div
              className="px-6 py-4 border-t"
              style={{ borderColor: "var(--color-border)" }}
            >
              <div className="flex items-center justify-between">
                <div className="text-sm" style={{ color: "var(--grey)" }}>
                  Preview of flashcard learning method
                </div>
                <button
                  onClick={handleClose}
                  className="px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                  style={{
                    backgroundColor: "var(--emerald)",
                    color: "var(--white)"
                  }}
                >
                  Close Preview
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
