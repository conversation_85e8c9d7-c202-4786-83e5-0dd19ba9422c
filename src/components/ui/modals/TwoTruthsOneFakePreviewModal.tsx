"use client";

import { motion, AnimatePresence } from "framer-motion";
import { HelpCircle, X } from "lucide-react";
import TwoTruthsOneFake, { exampleTwoTruthsOneFakeData } from "@/components/ui/learning/TwoTruthsOneFake";

interface TwoTruthsOneFakePreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  conceptTitle: string;
}

export default function TwoTruthsOneFakePreviewModal({
  isOpen,
  onClose,
  conceptTitle,
}: TwoTruthsOneFakePreviewModalProps) {
  const handleClose = () => {
    onClose();
  };

  const currentData = exampleTwoTruthsOneFakeData[0];

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          key="two-truths-preview-modal"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
          style={{ backgroundColor: "rgba(0, 0, 0, 0.5)" }}
        >
          <motion.div
            key="two-truths-preview-content"
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.95, opacity: 0 }}
            className="relative w-full max-w-2xl max-h-[90vh] overflow-hidden rounded-2xl"
            style={{ backgroundColor: "var(--white)" }}
          >
            {/* Header */}
            <div
              className="flex items-center justify-between p-6 border-b"
              style={{ borderColor: "var(--color-border)" }}
            >
              <div className="flex items-center gap-3">
                <div
                  className="p-2 rounded-lg"
                  style={{ backgroundColor: "var(--lime-green)", color: "var(--white)" }}
                >
                  <HelpCircle className="w-5 h-5" />
                </div>
                <div>
                  <h2
                    className="text-lg font-semibold"
                    style={{ color: "var(--charcoal)" }}
                  >
                    2 Truths 1 Fake Preview
                  </h2>
                  <p
                    className="text-sm"
                    style={{ color: "var(--grey)" }}
                  >
                    {conceptTitle} • Example Question
                  </p>
                </div>
              </div>
              <button
                onClick={handleClose}
                className="p-2 rounded-lg transition-colors duration-200"
                style={{ 
                  backgroundColor: "var(--color-muted)",
                  color: "var(--grey)"
                }}
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Content */}
            <div
              className="flex flex-col items-center justify-center p-8"
              style={{
                backgroundColor: "var(--color-muted)",
                minHeight: "450px"
              }}
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
              >
                <TwoTruthsOneFake
                  data={currentData}
                  onAnswer={(isCorrect, selectedIndex) => {
                    console.log(`Two Truths One Fake answered:`, { isCorrect, selectedIndex });
                  }}
                />
              </motion.div>

              {/* Category Display */}
              <div className="mt-8">
                <div
                  className="px-4 py-2 rounded-lg text-sm font-medium inline-block"
                  style={{
                    backgroundColor: "var(--white)",
                    color: "var(--charcoal)",
                    border: "1px solid var(--color-border)"
                  }}
                >
                  {currentData.category}
                </div>
              </div>

              {/* Instructions */}
              <p
                className="text-sm text-center mt-6 max-w-md"
                style={{ color: "var(--grey)" }}
              >
                <strong>Example:</strong> Read the three statements and identify which one is false. 
                Two statements are true, one is fake. This is a sample of how this learning method works.
              </p>
            </div>

            {/* Footer */}
            <div
              className="px-6 py-4 border-t"
              style={{ borderColor: "var(--color-border)" }}
            >
              <div className="flex items-center justify-between">
                <div className="text-sm" style={{ color: "var(--grey)" }}>
                  Preview of 2 Truths 1 Fake learning method
                </div>
                <button
                  onClick={handleClose}
                  className="px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                  style={{
                    backgroundColor: "var(--lime-green)",
                    color: "var(--white)"
                  }}
                >
                  Close Preview
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
