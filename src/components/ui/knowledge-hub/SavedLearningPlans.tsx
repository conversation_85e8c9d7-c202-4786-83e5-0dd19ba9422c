"use client";

import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { useUserContext } from "@/app/Firebase/Authentication/UserProvider";
import { 
  getCertificateLearningPlans, 
  deleteLearningPlan,
  type SavedLearningPlan 
} from "@/Services/savedLearningPlanService";
import EditPlanModal from "@/components/ui/modals/EditPlanModal";

interface SavedLearningPlansProps {
  certificateId: string;
  certificateName: string;
}

export default function SavedLearningPlans({ 
  certificateId, 
  certificateName 
}: SavedLearningPlansProps) {
  const { user } = useUserContext();
  const router = useRouter();
  const locale = useLocale();
  
  const [plans, setPlans] = useState<SavedLearningPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deletingPlanId, setDeletingPlanId] = useState<string | null>(null);
  const [editingPlan, setEditingPlan] = useState<SavedLearningPlan | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);

  useEffect(() => {
    if (user && certificateId) {
      loadPlans();
    }
  }, [user, certificateId]);

  const loadPlans = async () => {
    if (!user) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const userPlans = await getCertificateLearningPlans(user.uid, certificateId);
      setPlans(userPlans.sort((a, b) => 
        new Date(b.savedAt || 0).getTime() - new Date(a.savedAt || 0).getTime()
      ));
    } catch (err: any) {
      setError(err.message || "Failed to load saved plans");
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePlan = async (planId: string) => {
    if (!user || !planId) return;
    
    setDeletingPlanId(planId);
    try {
      await deleteLearningPlan(user.uid, planId);
      setPlans(plans.filter(plan => plan.id !== planId));
    } catch (err: any) {
      setError(err.message || "Failed to delete plan");
    } finally {
      setDeletingPlanId(null);
    }
  };

  const handleEditPlan = (plan: SavedLearningPlan) => {
    setEditingPlan(plan);
    setShowEditModal(true);
  };

  const handleViewPlan = (plan: SavedLearningPlan) => {
    // Store the plan in sessionStorage and navigate to the timeline
    const learningPlan = {
      keyword: plan.keyword,
      concepts: plan.concepts,
      certificateId: plan.certificateId,
      intensity: plan.intensity,
      generatedAt: plan.generatedAt,
    };
    
    const conceptName = encodeURIComponent(plan.keyword.trim());
    const storageKey = `learning-plan-${conceptName}`;
    sessionStorage.setItem(storageKey, JSON.stringify(learningPlan));
    
    // Navigate to timeline page
    router.push(`/${locale}/dashboard/knowledge-hub/certificates/${certificateId}/C${conceptName}`);
  };

  const handleModalClose = () => {
    setShowEditModal(false);
    setEditingPlan(null);
  };

  const handlePlanUpdated = () => {
    loadPlans(); // Refresh the plans list
  };

  const formatDate = (date: any) => {
    if (!date) return "Unknown";
    const d = date.toDate ? date.toDate() : new Date(date);
    return d.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    });
  };

  const getProgressPercentage = (plan: SavedLearningPlan) => {
    if (!plan.progress) return 0;
    return Math.round((plan.progress.completedConcepts / plan.progress.totalConcepts) * 100);
  };

  if (!user) {
    return (
      <div className="text-center py-8">
        <p style={{ color: "var(--grey)" }}>Please sign in to view your saved learning plans.</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <div 
            key={i}
            className="animate-pulse bg-gray-100 rounded-xl h-32"
          />
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div 
        className="p-4 rounded-lg border text-center"
        style={{ 
          backgroundColor: "#fef2f2", 
          borderColor: "#fecaca",
          color: "#dc2626" 
        }}
      >
        <p className="font-medium">Error loading saved plans</p>
        <p className="text-sm mt-1">{error}</p>
        <button
          onClick={loadPlans}
          className="mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (plans.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mb-4">
          <svg 
            className="w-16 h-16 mx-auto mb-4" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
            style={{ color: "var(--grey)" }}
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h3 className="text-lg font-semibold mb-2" style={{ color: "var(--charcoal)" }}>
          No Saved Learning Plans
        </h3>
        <p className="mb-4" style={{ color: "var(--grey)" }}>
          Create your first AI-generated learning plan to get started.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold" style={{ color: "var(--charcoal)" }}>
          Your Saved Plans ({plans.length})
        </h3>
        <button
          onClick={loadPlans}
          className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
          title="Refresh plans"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </button>
      </div>

      <AnimatePresence>
        {plans.map((plan, index) => (
          <motion.div
            key={plan.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-xl p-6 shadow-sm border hover:shadow-md transition-all duration-300"
            style={{ borderColor: "var(--color-border)" }}
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <h4 
                  className="text-lg font-semibold mb-2"
                  style={{ color: "var(--charcoal)" }}
                >
                  {plan.keyword}
                </h4>
                <div className="flex items-center gap-4 text-sm" style={{ color: "var(--grey)" }}>
                  <span>{plan.concepts.length} concepts</span>
                  <span>•</span>
                  <span className="capitalize">{plan.intensity} level</span>
                  <span>•</span>
                  <span>Saved {formatDate(plan.savedAt)}</span>
                </div>
              </div>

              {/* Progress Circle */}
              <div className="flex items-center gap-3">
                <div className="relative w-12 h-12">
                  <svg className="w-12 h-12 transform -rotate-90" viewBox="0 0 36 36">
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#e5e7eb"
                      strokeWidth="2"
                    />
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="var(--emerald)"
                      strokeWidth="2"
                      strokeDasharray={`${getProgressPercentage(plan)}, 100`}
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-xs font-medium" style={{ color: "var(--charcoal)" }}>
                      {getProgressPercentage(plan)}%
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Tags */}
            {plan.metadata?.tags && plan.metadata.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-4">
                {plan.metadata.tags.map((tag, tagIndex) => (
                  <span
                    key={tagIndex}
                    className="px-2 py-1 rounded-full text-xs font-medium"
                    style={{ 
                      backgroundColor: "var(--bright-green)", 
                      color: "var(--white)" 
                    }}
                  >
                    {tag}
                  </span>
                ))}
              </div>
            )}

            {/* Notes */}
            {plan.metadata?.notes && (
              <p 
                className="text-sm mb-4 italic"
                style={{ color: "var(--grey)" }}
              >
                "{plan.metadata.notes}"
              </p>
            )}

            {/* Action Buttons */}
            <div className="flex items-center gap-3">
              <button
                onClick={() => handleViewPlan(plan)}
                className="flex-1 inline-flex items-center justify-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                style={{ 
                  backgroundColor: "var(--emerald)", 
                  color: "var(--white)" 
                }}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Continue Learning
              </button>

              <button
                onClick={() => handleEditPlan(plan)}
                className="px-4 py-2 rounded-lg text-sm font-medium border transition-colors"
                style={{ 
                  borderColor: "var(--color-border)",
                  color: "var(--charcoal)" 
                }}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </button>

              <button
                onClick={() => handleDeletePlan(plan.id!)}
                disabled={deletingPlanId === plan.id}
                className="px-4 py-2 rounded-lg text-sm font-medium text-red-600 border border-red-300 hover:bg-red-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {deletingPlanId === plan.id ? (
                  <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                  </svg>
                ) : (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                )}
              </button>
            </div>
          </motion.div>
        ))}
      </AnimatePresence>

      {/* Edit Plan Modal */}
      {editingPlan && (
        <EditPlanModal
          isOpen={showEditModal}
          onClose={handleModalClose}
          onUpdated={handlePlanUpdated}
          plan={editingPlan}
        />
      )}
    </div>
  );
}
