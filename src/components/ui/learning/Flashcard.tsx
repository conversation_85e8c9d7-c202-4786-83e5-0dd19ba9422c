"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { RotateCcw, Eye, BookOpen, Lightbulb } from "lucide-react";

export interface FlashcardData {
  id: string;
  question: string;
  answer: string;
  category?: string;
  difficulty?: "easy" | "medium" | "hard";
  tags?: string[];
}

interface FlashcardProps {
  data: FlashcardData;
  className?: string;
  onFlip?: (isFlipped: boolean) => void;
  autoFlip?: boolean;
}

const difficultyColors = {
  easy: "var(--bright-green)",
  medium: "var(--lime-green)", 
  hard: "var(--emerald-deep)"
};

export default function Flashcard({
  data,
  className = "",
  onFlip,
  autoFlip = false
}: FlashcardProps) {
  const [isFlipped, setIsFlipped] = useState(false);

  const handleFlip = () => {
    const newFlipped = !isFlipped;
    setIsFlipped(newFlipped);
    onFlip?.(newFlipped);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      handleFlip();
    }
  };

  return (
    <div className={`w-96 h-56 ${className}`}>
      <motion.div
        className="relative w-full h-full cursor-pointer"
        onClick={handleFlip}
        onKeyDown={handleKeyDown}
        tabIndex={0}
        role="button"
        aria-label={`Flashcard: ${data.question}. ${isFlipped ? 'Showing answer' : 'Click to reveal answer'}`}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        style={{ perspective: "1000px" }}
      >
        <motion.div
          className="relative w-full h-full"
          animate={{ rotateY: isFlipped ? 180 : 0 }}
          transition={{ duration: 0.6, ease: "easeInOut" }}
          style={{ transformStyle: "preserve-3d" }}
        >
          {/* Front Side */}
          <motion.div
            className="absolute inset-0 w-full h-full rounded-2xl shadow-lg border"
            style={{
              backgroundColor: "var(--white)",
              borderColor: "var(--color-border)",
              backfaceVisibility: "hidden",
              boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
            }}
          >
            <div className="flex flex-col h-full p-6">
              {/* Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <div
                    className="p-2 rounded-lg"
                    style={{ backgroundColor: "var(--emerald)", color: "var(--white)" }}
                  >
                    <BookOpen className="w-4 h-4" />
                  </div>
                  {data.category && (
                    <span
                      className="text-xs font-medium px-2 py-1 rounded-full"
                      style={{
                        backgroundColor: "var(--color-muted)",
                        color: "var(--charcoal)"
                      }}
                    >
                      {data.category}
                    </span>
                  )}
                </div>
                {data.difficulty && (
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: difficultyColors[data.difficulty] }}
                    title={`Difficulty: ${data.difficulty}`}
                  />
                )}
              </div>

              {/* Question */}
              <div className="flex-1 flex items-center justify-center text-center">
                <p
                  className="text-lg font-semibold leading-relaxed"
                  style={{ color: "var(--charcoal)" }}
                >
                  {data.question}
                </p>
              </div>

              {/* Footer */}
              <div className="flex items-center justify-between mt-4">
                <div className="flex items-center gap-2">
                  <Eye className="w-4 h-4" style={{ color: "var(--grey)" }} />
                  <span className="text-xs" style={{ color: "var(--grey)" }}>
                    Click to reveal
                  </span>
                </div>
                <RotateCcw 
                  className="w-4 h-4" 
                  style={{ color: "var(--emerald)" }}
                />
              </div>
            </div>
          </motion.div>

          {/* Back Side */}
          <motion.div
            className="absolute inset-0 w-full h-full rounded-2xl shadow-lg border"
            style={{
              backgroundColor: "var(--emerald)",
              borderColor: "var(--emerald-deep)",
              backfaceVisibility: "hidden",
              transform: "rotateY(180deg)",
              boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
            }}
          >
            <div className="flex flex-col h-full p-6">
              {/* Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <div
                    className="p-2 rounded-lg"
                    style={{ backgroundColor: "rgba(255, 255, 255, 0.2)", color: "var(--white)" }}
                  >
                    <Lightbulb className="w-4 h-4" />
                  </div>
                  <span
                    className="text-xs font-medium px-2 py-1 rounded-full"
                    style={{
                      backgroundColor: "rgba(255, 255, 255, 0.2)",
                      color: "var(--white)"
                    }}
                  >
                    Answer
                  </span>
                </div>
                {data.difficulty && (
                  <div
                    className="w-3 h-3 rounded-full border-2"
                    style={{ borderColor: "var(--white)" }}
                    title={`Difficulty: ${data.difficulty}`}
                  />
                )}
              </div>

              {/* Answer */}
              <div className="flex-1 flex items-center justify-center text-center">
                <p
                  className="text-lg font-semibold leading-relaxed"
                  style={{ color: "var(--white)" }}
                >
                  {data.answer}
                </p>
              </div>

              {/* Footer */}
              <div className="flex items-center justify-between mt-4">
                <div className="flex items-center gap-2">
                  {data.tags && data.tags.length > 0 && (
                    <div className="flex gap-1">
                      {data.tags.slice(0, 2).map((tag) => (
                        <span
                          key={`${data.id}-${tag}`}
                          className="text-xs px-2 py-1 rounded-full"
                          style={{
                            backgroundColor: "rgba(255, 255, 255, 0.2)",
                            color: "var(--white)"
                          }}
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
                <RotateCcw 
                  className="w-4 h-4" 
                  style={{ color: "var(--white)" }}
                />
              </div>
            </div>
          </motion.div>
        </motion.div>
      </motion.div>
    </div>
  );
}

// Example flashcard data for preview
export const exampleFlashcards: FlashcardData[] = [
  {
    id: "example-1",
    question: "What is the primary purpose of data encryption in GDPR compliance?",
    answer: "To protect personal data from unauthorized access and ensure data security during processing and storage.",
    category: "Data Protection",
    difficulty: "medium",
    tags: ["GDPR", "Security"]
  }
];
