"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON>ir<PERSON>, User, <PERSON><PERSON>, ArrowRight, Lightbulb, RotateCcw } from "lucide-react";

export interface MentorMeData {
  id: string;
  topic: string;
  conversation: {
    id: string;
    type: "mentor" | "user";
    message: string;
    timestamp?: string;
  }[];
  category?: string;
  difficulty?: "easy" | "medium" | "hard";
  tags?: string[];
}

interface MentorMeProps {
  data: MentorMeData;
  className?: string;
  onInteraction?: (message: string) => void;
}

const difficultyColors = {
  easy: "var(--bright-green)",
  medium: "var(--lime-green)", 
  hard: "var(--emerald-deep)"
};

export default function MentorMe({ 
  data, 
  className = "", 
  onInteraction
}: MentorMeProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [userInput, setUserInput] = useState("");
  const [isCompleted, setIsCompleted] = useState(false);

  const handleNext = () => {
    if (currentStep < data.conversation.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      setIsCompleted(true);
    }
  };

  const handleUserResponse = () => {
    if (userInput.trim()) {
      onInteraction?.(userInput);
      setUserInput("");
      handleNext();
    }
  };

  const handleReset = () => {
    setCurrentStep(0);
    setUserInput("");
    setIsCompleted(false);
  };

  const currentMessage = data.conversation[currentStep];
  const isUserTurn = currentMessage?.type === "user";

  return (
    <div className={`w-96 h-80 ${className}`}>
      <div className="relative w-full h-full">
        <div
          className="w-full h-full rounded-2xl shadow-lg border flex flex-col"
          style={{
            backgroundColor: "var(--white)",
            borderColor: "var(--color-border)",
            boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
          }}
        >
          {/* Header */}
          <div
            className="flex items-center justify-between p-4 border-b"
            style={{ borderColor: "var(--color-border)" }}
          >
            <div className="flex items-center gap-2">
              <div
                className="p-2 rounded-lg"
                style={{ backgroundColor: "var(--emerald-deep)", color: "var(--white)" }}
              >
                <MessageCircle className="w-4 h-4" />
              </div>
              <div>
                <h3
                  className="text-sm font-semibold"
                  style={{ color: "var(--charcoal)" }}
                >
                  AI Mentor
                </h3>
                {data.category && (
                  <p
                    className="text-xs"
                    style={{ color: "var(--grey)" }}
                  >
                    {data.category}
                  </p>
                )}
              </div>
            </div>
            {data.difficulty && (
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: difficultyColors[data.difficulty] }}
                title={`Difficulty: ${data.difficulty}`}
              />
            )}
          </div>

          {/* Chat Area */}
          <div className="flex-1 p-4 overflow-y-auto">
            <AnimatePresence mode="wait">
              {!isCompleted ? (
                <motion.div
                  key={`step-${currentStep}`}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="space-y-3"
                >
                  {/* Topic Introduction */}
                  {currentStep === 0 && (
                    <div
                      className="p-3 rounded-lg"
                      style={{
                        backgroundColor: "var(--emerald)15",
                        border: "1px solid var(--emerald)"
                      }}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <Lightbulb className="w-4 h-4" style={{ color: "var(--emerald)" }} />
                        <span
                          className="text-sm font-semibold"
                          style={{ color: "var(--emerald)" }}
                        >
                          Learning Topic
                        </span>
                      </div>
                      <p
                        className="text-sm"
                        style={{ color: "var(--charcoal)" }}
                      >
                        {data.topic}
                      </p>
                    </div>
                  )}

                  {/* Current Message */}
                  {currentMessage && (
                    <div className={`flex ${currentMessage.type === "user" ? "justify-end" : "justify-start"}`}>
                      <div
                        className={`max-w-[80%] p-3 rounded-lg ${
                          currentMessage.type === "user" ? "rounded-br-sm" : "rounded-bl-sm"
                        }`}
                        style={{
                          backgroundColor: currentMessage.type === "user" 
                            ? "var(--emerald)" 
                            : "var(--color-muted)",
                          color: currentMessage.type === "user" 
                            ? "var(--white)" 
                            : "var(--charcoal)"
                        }}
                      >
                        <div className="flex items-center gap-2 mb-1">
                          {currentMessage.type === "user" ? (
                            <User className="w-3 h-3" />
                          ) : (
                            <Bot className="w-3 h-3" />
                          )}
                          <span className="text-xs font-medium">
                            {currentMessage.type === "user" ? "You" : "AI Mentor"}
                          </span>
                        </div>
                        <p className="text-sm leading-relaxed">
                          {currentMessage.message}
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Progress Indicator */}
                  <div className="flex items-center justify-center gap-1 mt-4">
                    {data.conversation.map((_, index) => (
                      <div
                        key={`progress-${index}`}
                        className="w-2 h-2 rounded-full"
                        style={{
                          backgroundColor: index <= currentStep 
                            ? "var(--emerald)" 
                            : "var(--color-border)"
                        }}
                      />
                    ))}
                  </div>
                </motion.div>
              ) : (
                <motion.div
                  key="completed"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="text-center space-y-4"
                >
                  <div
                    className="p-4 rounded-lg"
                    style={{
                      backgroundColor: "var(--bright-green)15",
                      border: "1px solid var(--bright-green)"
                    }}
                  >
                    <Lightbulb 
                      className="w-8 h-8 mx-auto mb-2" 
                      style={{ color: "var(--bright-green)" }} 
                    />
                    <h3
                      className="text-sm font-semibold mb-2"
                      style={{ color: "var(--charcoal)" }}
                    >
                      Guided Learning Complete!
                    </h3>
                    <p
                      className="text-xs"
                      style={{ color: "var(--grey)" }}
                    >
                      You've completed this guided learning session. Great job!
                    </p>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Footer */}
          <div
            className="p-4 border-t"
            style={{ borderColor: "var(--color-border)" }}
          >
            {!isCompleted ? (
              <div className="space-y-2">
                {isUserTurn ? (
                  <div className="flex gap-2">
                    <input
                      type="text"
                      value={userInput}
                      onChange={(e) => setUserInput(e.target.value)}
                      onKeyPress={(e) => e.key === "Enter" && handleUserResponse()}
                      placeholder="Type your response..."
                      className="flex-1 px-3 py-2 rounded-lg text-sm border"
                      style={{
                        backgroundColor: "var(--white)",
                        borderColor: "var(--color-border)",
                        color: "var(--charcoal)"
                      }}
                    />
                    <button
                      onClick={handleUserResponse}
                      disabled={!userInput.trim()}
                      className="px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 disabled:opacity-50"
                      style={{
                        backgroundColor: "var(--emerald)",
                        color: "var(--white)"
                      }}
                    >
                      <ArrowRight className="w-4 h-4" />
                    </button>
                  </div>
                ) : (
                  <button
                    onClick={handleNext}
                    className="w-full flex items-center justify-center gap-2 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                    style={{
                      backgroundColor: "var(--emerald)",
                      color: "var(--white)"
                    }}
                  >
                    Continue
                    <ArrowRight className="w-4 h-4" />
                  </button>
                )}
              </div>
            ) : (
              <button
                onClick={handleReset}
                className="w-full flex items-center justify-center gap-2 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                style={{
                  backgroundColor: "var(--emerald)",
                  color: "var(--white)"
                }}
              >
                <RotateCcw className="w-3 h-3" />
                Start Over
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Example data for preview
export const exampleMentorMeData: MentorMeData[] = [
  {
    id: "example-mentor-1",
    topic: "Understanding API Authentication",
    conversation: [
      {
        id: "1",
        type: "mentor",
        message: "Hi! I'm your AI mentor. Today we'll learn about API authentication. Can you tell me what you think authentication means in the context of APIs?"
      },
      {
        id: "2", 
        type: "user",
        message: "I think it's about verifying who is making the request?"
      },
      {
        id: "3",
        type: "mentor", 
        message: "Excellent! That's exactly right. Authentication verifies the identity of the client making the request. What do you think might happen if an API doesn't have authentication?"
      },
      {
        id: "4",
        type: "user",
        message: "Anyone could access it and potentially misuse the data?"
      },
      {
        id: "5",
        type: "mentor",
        message: "Perfect understanding! Without authentication, APIs would be vulnerable to unauthorized access, data breaches, and abuse. You've grasped the core concept beautifully!"
      }
    ],
    category: "API Security",
    difficulty: "medium",
    tags: ["APIs", "Security", "Authentication"]
  }
];
