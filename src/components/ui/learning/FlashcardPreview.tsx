"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON>, Play, RotateCcw, Shuffle, Settings } from "lucide-react";
import Flashcard, { exampleFlashcards, type FlashcardData } from "./Flashcard";
import { Button } from "@/components/ui/button";

interface FlashcardPreviewProps {
  className?: string;
}

export default function FlashcardPreview({ className = "" }: FlashcardPreviewProps) {
  const [showPreview, setShowPreview] = useState(false);
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [selectedSize, setSelectedSize] = useState<"sm" | "md" | "lg">("md");

  const currentCard = exampleFlashcards[currentCardIndex];

  const handleNextCard = () => {
    setCurrentCardIndex((prev) => (prev + 1) % exampleFlashcards.length);
  };

  const handlePrevCard = () => {
    setCurrentCardIndex((prev) => (prev - 1 + exampleFlashcards.length) % exampleFlashcards.length);
  };

  const handleShuffle = () => {
    const randomIndex = Math.floor(Math.random() * exampleFlashcards.length);
    setCurrentCardIndex(randomIndex);
  };

  const togglePreview = () => {
    setShowPreview(!showPreview);
    if (!showPreview) {
      setCurrentCardIndex(0);
      setIsPlaying(false);
    }
  };

  return (
    <div className={`w-full ${className}`}>
      {/* Preview Button */}
      {!showPreview && (
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div
            className="inline-flex flex-col items-center gap-6 p-8 rounded-2xl border"
            style={{
              backgroundColor: "var(--white)",
              borderColor: "var(--color-border)",
              boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
            }}
          >
            <div
              className="p-6 rounded-full"
              style={{ backgroundColor: "var(--emerald)" }}
            >
              <Eye className="w-8 h-8" style={{ color: "var(--white)" }} />
            </div>
            
            <div className="text-center max-w-md">
              <h3
                className="text-2xl font-bold mb-3"
                style={{ color: "var(--charcoal)" }}
              >
                Flashcard Component Preview
              </h3>
              <p
                className="text-base leading-relaxed mb-6"
                style={{ color: "var(--grey)" }}
              >
                Experience our beautiful flashcard component with smooth animations, 
                DTC color scheme, and interactive features. Perfect for learning and memorization.
              </p>
              
              <div className="flex flex-wrap gap-2 justify-center mb-6">
                {["Interactive Flip", "DTC Colors", "Responsive", "Accessible"].map((feature) => (
                  <span
                    key={feature}
                    className="px-3 py-1 rounded-full text-sm font-medium"
                    style={{
                      backgroundColor: "var(--bright-green)",
                      color: "var(--white)"
                    }}
                  >
                    {feature}
                  </span>
                ))}
              </div>
            </div>

            <Button
              onClick={togglePreview}
              className="inline-flex items-center gap-2 px-8 py-3 rounded-xl text-base font-semibold transition-all duration-200 hover:scale-[1.02] hover:shadow-lg"
              style={{
                backgroundColor: "var(--emerald)",
                color: "var(--white)"
              }}
            >
              <Play className="w-5 h-5" />
              Preview Flashcards
            </Button>
          </div>
        </motion.div>
      )}

      {/* Preview Interface */}
      <AnimatePresence>
        {showPreview && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.3 }}
            className="w-full"
          >
            {/* Header */}
            <div
              className="flex items-center justify-between p-6 rounded-t-2xl border-b"
              style={{
                backgroundColor: "var(--white)",
                borderColor: "var(--color-border)"
              }}
            >
              <div className="flex items-center gap-4">
                <div
                  className="p-2 rounded-lg"
                  style={{ backgroundColor: "var(--emerald)", color: "var(--white)" }}
                >
                  <Eye className="w-5 h-5" />
                </div>
                <div>
                  <h3
                    className="text-lg font-semibold"
                    style={{ color: "var(--charcoal)" }}
                  >
                    Flashcard Preview
                  </h3>
                  <p
                    className="text-sm"
                    style={{ color: "var(--grey)" }}
                  >
                    Card {currentCardIndex + 1} of {exampleFlashcards.length}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                {/* Size Selector */}
                <div className="flex items-center gap-1 p-1 rounded-lg" style={{ backgroundColor: "var(--color-muted)" }}>
                  {(["sm", "md", "lg"] as const).map((size) => (
                    <button
                      key={size}
                      onClick={() => setSelectedSize(size)}
                      className={`px-3 py-1 rounded text-xs font-medium transition-all duration-200 ${
                        selectedSize === size ? 'shadow-sm' : ''
                      }`}
                      style={{
                        backgroundColor: selectedSize === size ? "var(--emerald)" : "transparent",
                        color: selectedSize === size ? "var(--white)" : "var(--charcoal)"
                      }}
                    >
                      {size.toUpperCase()}
                    </button>
                  ))}
                </div>

                <button
                  onClick={handleShuffle}
                  className="p-2 rounded-lg transition-colors duration-200"
                  style={{
                    backgroundColor: "var(--color-muted)",
                    color: "var(--charcoal)"
                  }}
                  title="Shuffle cards"
                >
                  <Shuffle className="w-4 h-4" />
                </button>

                <button
                  onClick={togglePreview}
                  className="px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                  style={{
                    backgroundColor: "var(--color-muted)",
                    color: "var(--charcoal)"
                  }}
                >
                  Close
                </button>
              </div>
            </div>

            {/* Flashcard Display */}
            <div
              className="flex flex-col items-center justify-center p-12 rounded-b-2xl"
              style={{
                backgroundColor: "var(--color-muted)",
                minHeight: "400px"
              }}
            >
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentCardIndex}
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -50 }}
                  transition={{ duration: 0.3 }}
                >
                  <Flashcard
                    data={currentCard}
                    size={selectedSize}
                    onFlip={(isFlipped) => {
                      console.log(`Card ${currentCardIndex + 1} flipped:`, isFlipped);
                    }}
                  />
                </motion.div>
              </AnimatePresence>

              {/* Navigation */}
              <div className="flex items-center gap-4 mt-8">
                <button
                  onClick={handlePrevCard}
                  className="p-3 rounded-xl transition-all duration-200 hover:scale-105"
                  style={{
                    backgroundColor: "var(--white)",
                    color: "var(--charcoal)",
                    border: "1px solid var(--color-border)"
                  }}
                  disabled={exampleFlashcards.length <= 1}
                >
                  <RotateCcw className="w-5 h-5 rotate-180" />
                </button>

                <div
                  className="px-4 py-2 rounded-lg text-sm font-medium"
                  style={{
                    backgroundColor: "var(--white)",
                    color: "var(--charcoal)",
                    border: "1px solid var(--color-border)"
                  }}
                >
                  {currentCard.category}
                </div>

                <button
                  onClick={handleNextCard}
                  className="p-3 rounded-xl transition-all duration-200 hover:scale-105"
                  style={{
                    backgroundColor: "var(--white)",
                    color: "var(--charcoal)",
                    border: "1px solid var(--color-border)"
                  }}
                  disabled={exampleFlashcards.length <= 1}
                >
                  <RotateCcw className="w-5 h-5" />
                </button>
              </div>

              {/* Instructions */}
              <p
                className="text-sm text-center mt-6 max-w-md"
                style={{ color: "var(--grey)" }}
              >
                Click the flashcard to flip it and reveal the answer. Use the navigation buttons to browse through different examples.
              </p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
