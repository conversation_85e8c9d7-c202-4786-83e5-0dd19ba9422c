"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { BookOpen, Palette, Smartphone, Users, Code, Zap } from "lucide-react";
import Flashcard, { exampleFlashcards } from "./Flashcard";
import FlashcardPreview from "./FlashcardPreview";

export default function FlashcardDemo() {
  const [selectedDemo, setSelectedDemo] = useState<"preview" | "sizes" | "difficulties" | "features">("preview");

  const features = [
    {
      icon: <Zap className="w-6 h-6" />,
      title: "Smooth Animations",
      description: "Beautiful flip animations powered by Framer Motion"
    },
    {
      icon: <Palette className="w-6 h-6" />,
      title: "DTC Color Scheme",
      description: "Consistent with DTC brand colors and design system"
    },
    {
      icon: <Smartphone className="w-6 h-6" />,
      title: "Responsive Design",
      description: "Works perfectly on all screen sizes and devices"
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: "Accessible",
      description: "Full keyboard navigation and screen reader support"
    },
    {
      icon: <Code className="w-6 h-6" />,
      title: "TypeScript Ready",
      description: "Fully typed with comprehensive interfaces"
    },
    {
      icon: <BookOpen className="w-6 h-6" />,
      title: "Learning Focused",
      description: "Designed specifically for educational content"
    }
  ];

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1
            className="text-4xl font-bold mb-4"
            style={{ color: "var(--charcoal)" }}
          >
            Flashcard Component
          </h1>
          <p
            className="text-lg max-w-2xl mx-auto leading-relaxed"
            style={{ color: "var(--grey)" }}
          >
            A beautiful, interactive flashcard component built with React, TypeScript, and Framer Motion. 
            Perfect for learning applications with DTC's elegant design system.
          </p>
        </motion.div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex justify-center">
        <div
          className="inline-flex p-1 rounded-xl"
          style={{ backgroundColor: "var(--color-muted)" }}
        >
          {[
            { id: "preview", label: "Interactive Preview" },
            { id: "sizes", label: "Size Variants" },
            { id: "difficulties", label: "Difficulty Levels" },
            { id: "features", label: "Features" }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setSelectedDemo(tab.id as any)}
              className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                selectedDemo === tab.id ? 'shadow-sm' : ''
              }`}
              style={{
                backgroundColor: selectedDemo === tab.id ? "var(--emerald)" : "transparent",
                color: selectedDemo === tab.id ? "var(--white)" : "var(--charcoal)"
              }}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <motion.div
        key={selectedDemo}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
        className="min-h-[500px]"
      >
        {selectedDemo === "preview" && (
          <FlashcardPreview />
        )}

        {selectedDemo === "sizes" && (
          <div className="space-y-8">
            <div className="text-center">
              <h2
                className="text-2xl font-semibold mb-3"
                style={{ color: "var(--charcoal)" }}
              >
                Size Variants
              </h2>
              <p
                className="text-base"
                style={{ color: "var(--grey)" }}
              >
                Choose from three different sizes to fit your layout needs
              </p>
            </div>
            
            <div className="flex flex-wrap items-center justify-center gap-8">
              {(["sm", "md", "lg"] as const).map((size) => (
                <div key={size} className="text-center space-y-4">
                  <Flashcard
                    data={exampleFlashcards[0]}
                    size={size}
                  />
                  <div>
                    <p
                      className="font-semibold text-sm"
                      style={{ color: "var(--charcoal)" }}
                    >
                      {size.toUpperCase()} Size
                    </p>
                    <p
                      className="text-xs"
                      style={{ color: "var(--grey)" }}
                    >
                      {size === "sm" && "256×160px"}
                      {size === "md" && "320×192px"}
                      {size === "lg" && "384×224px"}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {selectedDemo === "difficulties" && (
          <div className="space-y-8">
            <div className="text-center">
              <h2
                className="text-2xl font-semibold mb-3"
                style={{ color: "var(--charcoal)" }}
              >
                Difficulty Levels
              </h2>
              <p
                className="text-base"
                style={{ color: "var(--grey)" }}
              >
                Visual indicators help learners understand content complexity
              </p>
            </div>
            
            <div className="flex flex-wrap items-center justify-center gap-8">
              {exampleFlashcards.map((card, index) => (
                <div key={card.id} className="text-center space-y-4">
                  <Flashcard data={card} />
                  <div>
                    <p
                      className="font-semibold text-sm capitalize"
                      style={{ color: "var(--charcoal)" }}
                    >
                      {card.difficulty} Level
                    </p>
                    <p
                      className="text-xs"
                      style={{ color: "var(--grey)" }}
                    >
                      {card.category}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {selectedDemo === "features" && (
          <div className="space-y-8">
            <div className="text-center">
              <h2
                className="text-2xl font-semibold mb-3"
                style={{ color: "var(--charcoal)" }}
              >
                Component Features
              </h2>
              <p
                className="text-base"
                style={{ color: "var(--grey)" }}
              >
                Built with modern web standards and best practices
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {features.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  className="p-6 rounded-xl border"
                  style={{
                    backgroundColor: "var(--white)",
                    borderColor: "var(--color-border)"
                  }}
                >
                  <div
                    className="p-3 rounded-lg w-fit mb-4"
                    style={{ backgroundColor: "var(--emerald)", color: "var(--white)" }}
                  >
                    {feature.icon}
                  </div>
                  <h3
                    className="text-lg font-semibold mb-2"
                    style={{ color: "var(--charcoal)" }}
                  >
                    {feature.title}
                  </h3>
                  <p
                    className="text-sm leading-relaxed"
                    style={{ color: "var(--grey)" }}
                  >
                    {feature.description}
                  </p>
                </motion.div>
              ))}
            </div>

            {/* Code Example */}
            <div
              className="p-6 rounded-xl border"
              style={{
                backgroundColor: "var(--charcoal)",
                borderColor: "var(--color-border)"
              }}
            >
              <h3
                className="text-lg font-semibold mb-4"
                style={{ color: "var(--white)" }}
              >
                Usage Example
              </h3>
              <pre
                className="text-sm overflow-x-auto"
                style={{ color: "var(--bright-green)" }}
              >
{`import Flashcard from '@/components/ui/learning/Flashcard';

const flashcardData = {
  id: "1",
  question: "What is React?",
  answer: "A JavaScript library for building user interfaces",
  category: "Frontend",
  difficulty: "easy",
  tags: ["React", "JavaScript"]
};

<Flashcard 
  data={flashcardData}
  size="md"
  onFlip={(isFlipped) => console.log('Flipped:', isFlipped)}
/>`}
              </pre>
            </div>
          </div>
        )}
      </motion.div>
    </div>
  );
}
