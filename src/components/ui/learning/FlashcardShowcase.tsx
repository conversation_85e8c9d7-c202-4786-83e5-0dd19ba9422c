"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON>, ArrowR<PERSON>, Spark<PERSON> } from "lucide-react";
import Flashcard, { exampleFlashcards } from "./Flashcard";

interface FlashcardShowcaseProps {
  title?: string;
  description?: string;
  showPreviewButton?: boolean;
  className?: string;
}

export default function FlashcardShowcase({
  title = "Interactive Flashcards",
  description = "Beautiful, responsive flashcards with smooth animations and DTC styling",
  showPreviewButton = true,
  className = ""
}: FlashcardShowcaseProps) {
  const [showDemo, setShowDemo] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);

  const handlePreview = () => {
    setShowDemo(true);
  };

  const handleNext = () => {
    setCurrentIndex((prev) => (prev + 1) % exampleFlashcards.length);
  };

  return (
    <div className={`w-full ${className}`}>
      {!showDemo ? (
        // Preview State
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center space-y-6"
        >
          <div
            className="inline-flex items-center gap-3 px-6 py-8 rounded-2xl border"
            style={{
              backgroundColor: "var(--white)",
              borderColor: "var(--color-border)",
              boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
            }}
          >
            <div
              className="p-4 rounded-full"
              style={{ backgroundColor: "var(--emerald)" }}
            >
              <Sparkles className="w-6 h-6" style={{ color: "var(--white)" }} />
            </div>
            
            <div className="text-left">
              <h3
                className="text-xl font-bold mb-2"
                style={{ color: "var(--charcoal)" }}
              >
                {title}
              </h3>
              <p
                className="text-sm max-w-md"
                style={{ color: "var(--grey)" }}
              >
                {description}
              </p>
            </div>
          </div>

          {showPreviewButton && (
            <motion.button
              onClick={handlePreview}
              className="inline-flex items-center gap-2 px-6 py-3 rounded-xl text-sm font-semibold transition-all duration-200 hover:scale-[1.02] hover:shadow-lg"
              style={{
                backgroundColor: "var(--emerald)",
                color: "var(--white)"
              }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Eye className="w-4 h-4" />
              Preview Component
              <ArrowRight className="w-4 h-4" />
            </motion.button>
          )}
        </motion.div>
      ) : (
        // Demo State
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.4 }}
          className="space-y-6"
        >
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h3
                className="text-lg font-semibold"
                style={{ color: "var(--charcoal)" }}
              >
                Flashcard Component Demo
              </h3>
              <p
                className="text-sm"
                style={{ color: "var(--grey)" }}
              >
                Click the card to flip it and see the answer
              </p>
            </div>
            <button
              onClick={() => setShowDemo(false)}
              className="px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
              style={{
                backgroundColor: "var(--color-muted)",
                color: "var(--charcoal)"
              }}
            >
              Close Demo
            </button>
          </div>

          {/* Flashcard Display */}
          <div className="flex flex-col items-center space-y-6">
            <motion.div
              key={currentIndex}
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Flashcard
                data={exampleFlashcards[currentIndex]}
                size="md"
                onFlip={(isFlipped) => {
                  console.log(`Flashcard flipped: ${isFlipped}`);
                }}
              />
            </motion.div>

            {/* Navigation */}
            <div className="flex items-center gap-4">
              <div
                className="px-4 py-2 rounded-lg text-sm"
                style={{
                  backgroundColor: "var(--color-muted)",
                  color: "var(--charcoal)"
                }}
              >
                {currentIndex + 1} of {exampleFlashcards.length}
              </div>
              
              <button
                onClick={handleNext}
                className="inline-flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:scale-[1.02]"
                style={{
                  backgroundColor: "var(--emerald)",
                  color: "var(--white)"
                }}
              >
                Next Card
                <ArrowRight className="w-4 h-4" />
              </button>
            </div>

            {/* Features */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 w-full max-w-2xl">
              {[
                "Smooth Animations",
                "DTC Colors",
                "Responsive",
                "Accessible"
              ].map((feature) => (
                <div
                  key={feature}
                  className="text-center p-3 rounded-lg"
                  style={{
                    backgroundColor: "var(--white)",
                    border: "1px solid var(--color-border)"
                  }}
                >
                  <p
                    className="text-xs font-medium"
                    style={{ color: "var(--charcoal)" }}
                  >
                    {feature}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
}
