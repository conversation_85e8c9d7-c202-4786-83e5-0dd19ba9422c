# Flashcard Component

A beautiful, interactive flashcard component built with React, TypeScript, and Framer Motion, following DTC's design system.

## Features

- ✨ **Smooth Animations**: Beautiful flip animations powered by Framer Motion
- 🎨 **DTC Color Scheme**: Consistent with DTC brand colors and design system
- 📱 **Responsive Design**: Works perfectly on all screen sizes and devices
- ♿ **Accessible**: Full keyboard navigation and screen reader support
- 🔧 **TypeScript Ready**: Fully typed with comprehensive interfaces
- 📚 **Learning Focused**: Designed specifically for educational content

## Components

### `Flashcard`
The main flashcard component with flip animation.

```tsx
import { Flashcard } from '@/components/ui/learning';

const flashcardData = {
  id: "1",
  question: "What is React?",
  answer: "A JavaScript library for building user interfaces",
  category: "Frontend",
  difficulty: "easy",
  tags: ["React", "JavaScript"]
};

<Flashcard 
  data={flashcardData}
  size="md"
  onFlip={(isFlipped) => console.log('Flipped:', isFlipped)}
/>
```

### `FlashcardPreview`
Interactive preview component with navigation and size controls.

```tsx
import { FlashcardPreview } from '@/components/ui/learning';

<FlashcardPreview />
```

### `FlashcardShowcase`
Simple showcase component for demonstrations.

```tsx
import { FlashcardShowcase } from '@/components/ui/learning';

<FlashcardShowcase 
  title="Custom Title"
  description="Custom description"
  showPreviewButton={true}
/>
```

### `FlashcardDemo`
Complete demo page with all features and examples.

```tsx
import { FlashcardDemo } from '@/components/ui/learning';

<FlashcardDemo />
```

## Props

### FlashcardData Interface

```typescript
interface FlashcardData {
  id: string;
  question: string;
  answer: string;
  category?: string;
  difficulty?: "easy" | "medium" | "hard";
  tags?: string[];
}
```

### Flashcard Props

```typescript
interface FlashcardProps {
  data: FlashcardData;
  className?: string;
  onFlip?: (isFlipped: boolean) => void;
  autoFlip?: boolean;
  size?: "sm" | "md" | "lg";
}
```

## Sizes

- **Small (sm)**: 256×160px
- **Medium (md)**: 320×192px  
- **Large (lg)**: 384×224px

## Difficulty Colors

- **Easy**: Bright Green (`var(--bright-green)`)
- **Medium**: Lime Green (`var(--lime-green)`)
- **Hard**: Deep Emerald (`var(--emerald-deep)`)

## Demo Page

Visit `/dashboard/flashcard-demo` to see the component in action with all features and examples.

## Accessibility

- Full keyboard navigation support
- Screen reader compatible
- ARIA labels and roles
- Focus management
- High contrast support

## DTC Design System

The component follows DTC's design guidelines:
- Uses semantic color tokens from `globals.css`
- Consistent typography with Montserrat/Glancyr fonts
- Elegant borders and shadows
- No black borders or harsh contrasts
- Smooth, subtle animations
