# Flashcard Component

A beautiful, interactive flashcard component built with React, TypeScript, and Framer Motion, following DTC's design system.

## Features

- ✨ **Smooth Animations**: Beautiful flip animations powered by Framer Motion
- 🎨 **DTC Color Scheme**: Consistent with DTC brand colors and design system
- 📱 **Responsive Design**: Works perfectly on all screen sizes and devices
- ♿ **Accessible**: Full keyboard navigation and screen reader support
- 🔧 **TypeScript Ready**: Fully typed with comprehensive interfaces
- 📚 **Learning Focused**: Designed specifically for educational content
- 👁️ **Preview Integration**: Eye icon preview in learning method selection

## Components

### `Flashcard`
The main flashcard component with flip animation (large size only).

```tsx
import { Flashcard } from '@/components/ui/learning';

const flashcardData = {
  id: "1",
  question: "What is React?",
  answer: "A JavaScript library for building user interfaces",
  category: "Frontend",
  difficulty: "easy",
  tags: ["React", "JavaScript"]
};

<Flashcard
  data={flashcardData}
  onFlip={(isFlipped) => console.log('Flipped:', isFlipped)}
/>
```

### `FlashcardPreviewModal`
Modal component that shows flashcard preview when clicking the eye icon in learning method selection.

```tsx
import FlashcardPreviewModal from '@/components/ui/modals/FlashcardPreviewModal';

<FlashcardPreviewModal
  isOpen={showPreview}
  onClose={() => setShowPreview(false)}
  conceptTitle="Data Protection"
/>
```

## Props

### FlashcardData Interface

```typescript
interface FlashcardData {
  id: string;
  question: string;
  answer: string;
  category?: string;
  difficulty?: "easy" | "medium" | "hard";
  tags?: string[];
}
```

### Flashcard Props

```typescript
interface FlashcardProps {
  data: FlashcardData;
  className?: string;
  onFlip?: (isFlipped: boolean) => void;
  autoFlip?: boolean;
}
```

## Size

- **Large**: 384×224px (fixed size for consistency)

## Difficulty Colors

- **Easy**: Bright Green (`var(--bright-green)`)
- **Medium**: Lime Green (`var(--lime-green)`)
- **Hard**: Deep Emerald (`var(--emerald-deep)`)

## Integration

The flashcard component is integrated into the learning method selection modal. When users click "Start Learning" on a concept and select "Flashcards" as a learning method, they can click the eye icon to preview the flashcard component.

## Accessibility

- Full keyboard navigation support
- Screen reader compatible
- ARIA labels and roles
- Focus management
- High contrast support

## DTC Design System

The component follows DTC's design guidelines:
- Uses semantic color tokens from `globals.css`
- Consistent typography with Montserrat/Glancyr fonts
- Elegant borders and shadows
- No black borders or harsh contrasts
- Smooth, subtle animations
