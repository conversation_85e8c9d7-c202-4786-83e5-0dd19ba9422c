"use client";
import {
  useMotionValueEvent,
  useScroll,
  useTransform,
  motion,
} from "framer-motion";
import React, { useEffect, useRef, useState } from "react";
import LearningMethodModal from "@/components/ui/modals/LearningMethodModal";

interface LearningConcept {
  title: string;
  description: string;
  prerequisites: string[];
  focusArea: string;
}

interface LearningTimelineProps {
  keyword: string;
  concepts: LearningConcept[];
}

export const LearningTimeline = ({ keyword, concepts }: LearningTimelineProps) => {
  const ref = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState(0);
  const [showMethodModal, setShowMethodModal] = useState(false);
  const [selectedConcept, setSelectedConcept] = useState<LearningConcept | null>(null);

  useEffect(() => {
    if (ref.current) {
      const rect = ref.current.getBoundingClientRect();
      setHeight(rect.height);
    }
  }, [ref]);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start 10%", "end 50%"],
  });

  const heightTransform = useTransform(scrollYProgress, [0, 1], [0, height]);
  const opacityTransform = useTransform(scrollYProgress, [0, 0.1], [0, 1]);

  const handleStartLearning = (concept: LearningConcept) => {
    setSelectedConcept(concept);
    setShowMethodModal(true);
  };

  const handleMethodSelection = (selectedMethods: string[]) => {
    console.log("Selected learning methods:", selectedMethods);
    console.log("For concept:", selectedConcept?.title);
    // TODO: Implement learning method logic here
    setShowMethodModal(false);
    setSelectedConcept(null);
  };

  const handleCloseModal = () => {
    setShowMethodModal(false);
    setSelectedConcept(null);
  };

  return (
    <div
      className="w-full bg-white font-sans md:px-10"
      ref={containerRef}
      style={{ backgroundColor: "var(--white)" }}
    >
      <div ref={ref} className="relative max-w-7xl mx-auto py-8">
        {concepts.map((concept, index) => (
          <div
            key={index}
            className="flex justify-start pt-10 md:pt-40 md:gap-10"
          >
            <div className="sticky flex flex-col md:flex-row z-40 items-center top-40 self-start max-w-xs lg:max-w-md xl:max-w-lg md:w-full">
              <div
                className="h-10 absolute left-3 md:left-3 w-10 rounded-full flex items-center justify-center"
                style={{ backgroundColor: "var(--white)" }}
              >
                <div
                  className="h-4 w-4 rounded-full p-2"
                  style={{
                    backgroundColor: "var(--emerald)",
                    border: `2px solid var(--emerald-deep)`
                  }}
                />
              </div>
              <h3
                className="hidden md:block text-lg md:pl-20 md:text-2xl lg:text-3xl xl:text-4xl font-bold break-words leading-tight"
                style={{ color: "var(--charcoal)" }}
              >
                {concept.title}
              </h3>
            </div>

            <div className="relative pl-20 pr-4 md:pl-4 w-full">
              <h3
                className="md:hidden block text-2xl mb-4 text-left font-bold"
                style={{ color: "var(--charcoal)" }}
              >
                {concept.title}
              </h3>

              {/* Enhanced Content Card */}
              <div
                className="bg-white rounded-2xl p-6 md:p-8 shadow-sm border transition-all duration-300 hover:shadow-md"
                style={{
                  borderColor: "var(--color-border)",
                  background: "linear-gradient(145deg, var(--white) 0%, #fafafa 100%)"
                }}
              >
                {/* Header with Focus Area and Progress */}
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div
                      className="px-4 py-2 rounded-full text-sm font-medium"
                      style={{
                        backgroundColor: "var(--emerald)",
                        color: "var(--white)"
                      }}
                    >
                      {concept.focusArea}
                    </div>
                    <div
                      className="px-3 py-1 rounded-full text-xs font-medium"
                      style={{
                        backgroundColor: "var(--bright-green)",
                        color: "var(--white)"
                      }}
                    >
                      Concept {index + 1}
                    </div>
                  </div>

                  {/* Estimated Time */}
                  <div className="text-right">
                    <div
                      className="text-sm font-medium"
                      style={{ color: "var(--emerald-deep)" }}
                    >
                      ~30 min
                    </div>
                    <div
                      className="text-xs"
                      style={{ color: "var(--grey)" }}
                    >
                      Est. time
                    </div>
                  </div>
                </div>

                {/* Description */}
                <div
                  className="text-base leading-relaxed mb-6"
                  style={{ color: "var(--charcoal)" }}
                >
                  {concept.description}
                </div>



                {/* Action Buttons */}
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => handleStartLearning(concept)}
                    className="flex-1 inline-flex items-center justify-center gap-2 px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:scale-[1.02] hover:shadow-lg"
                    style={{
                      backgroundColor: "var(--emerald)",
                      color: "var(--white)",
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = "var(--emerald-deep)";
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = "var(--emerald)";
                    }}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Start Learning
                  </button>

                  <button
                    className="px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:scale-[1.02]"
                    style={{
                      backgroundColor: "var(--color-muted)",
                      color: "var(--charcoal)",
                      border: "1px solid var(--color-border)"
                    }}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
        
        <div
          style={{
            height: height + "px",
          }}
          className="absolute md:left-8 left-8 top-0 overflow-hidden w-[2px] bg-[linear-gradient(to_bottom,var(--tw-gradient-stops))] from-transparent from-[0%] via-neutral-200 to-transparent to-[99%] [mask-image:linear-gradient(to_bottom,transparent_0%,black_10%,black_90%,transparent_100%)]"
        >
          <motion.div
            style={{
              height: heightTransform,
              opacity: opacityTransform,
            }}
            className="absolute inset-x-0 top-0 w-[2px] rounded-full"
            animate={{
              background: [
                "linear-gradient(to top, var(--emerald) 0%, var(--bright-green) 50%, var(--lime-green) 100%)",
                "linear-gradient(to top, var(--bright-green) 0%, var(--emerald) 50%, var(--emerald-deep) 100%)",
                "linear-gradient(to top, var(--emerald) 0%, var(--bright-green) 50%, var(--lime-green) 100%)",
              ],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        </div>
      </div>

      {/* Learning Method Modal */}
      <LearningMethodModal
        isOpen={showMethodModal}
        onClose={handleCloseModal}
        onStart={handleMethodSelection}
        conceptTitle={selectedConcept?.title || ""}
      />
    </div>
  );
};
